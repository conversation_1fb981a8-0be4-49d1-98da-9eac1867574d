// Analyzer.tsx - COMPLETE VERSION WITH FIXED SELECTIVE CHECK SYSTEM

import React, { useState } from "react";
import { useAuth } from "@/contexts/AuthContext";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Calendar } from "@/components/ui/calendar";
import { Checkbox } from "@/components/ui/checkbox";
import { Badge } from "@/components/ui/badge";
import { CalendarIcon, FileText, AlertCircle, Upload, X, CheckCircle, Clock, Settings } from "lucide-react";
import { cn } from "@/lib/utils";
import { useToast } from "@/hooks/use-toast";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { format } from "date-fns";
import { useNavigate } from "react-router-dom";

// Import processing functions
import {
  DocumentFiles,
  AnalysisParameters,
  ProcessingProgress,
  processDocumentsForAnalysis,
  getAnalysisSummary
} from "@/lib/workingDocumentProcessor";
import { CheckResult } from "@/lib/checkDefinitions";
import { saveAnalysisResults } from "@/lib/firebaseStorageService";
import AnalysisResults from "@/components/analyzer/AnalysisResults";

// ===================================================================
// INTERFACES FOR SELECTIVE CHECK SYSTEM
// ===================================================================

interface CheckOption {
  id: string;
  name: string;
  description: string;
  category: string;
  documentTypes: string[];
  estimatedTime: string; // e.g., "30 seconds"
  isAvailable: boolean; // based on uploaded documents and parameters
  isSelected: boolean; // user's choice
}

interface CheckCategory {
  name: string;
  checks: CheckOption[];
  totalSelected: number;
  totalAvailable: number;
}

interface AnalysisData {
  id: string;
  parameters: AnalysisParameters;
  results: Record<string, CheckResult>;
  documents: DocumentFiles;
  timestamp: number;
  summary: {
    total: number;
    compliant: number;
    nonCompliant: number;
    compliancePercentage: number;
  };
}

// ===================================================================
// FIXED AND COMPLETE getAvailableChecksForSelection FUNCTION
// ===================================================================

// Complete getAvailableChecksForSelection function for Analyzer.tsx
// Replace your existing function with this complete version

// Complete getAvailableChecksForSelection function for Analyzer.tsx
// Replace your existing function with this complete version

function getAvailableChecksForSelection(
  documents: DocumentFiles,
  parameters: AnalysisParameters
): CheckOption[] {
  const availableChecks: CheckOption[] = [];
  const addedCheckIds = new Set<string>(); // Prevent duplicates

  // Helper function to add check with duplicate prevention
  const addCheck = (check: CheckOption) => {
    if (!addedCheckIds.has(check.id)) {
      availableChecks.push(check);
      addedCheckIds.add(check.id);
    }
  };

  // ===================================================================
  // AUDIT REPORT CHECKS (SINGLE DOCUMENT)
  // ===================================================================
  if (documents.audit_report) {
    // Always available audit report checks
    [
      {
        id: 'audit_title',
        name: 'Independent Auditor\'s Report Title',
        description: 'Checks for exact heading "Independent Auditor\'s Report"',
        category: 'Basic Compliance',
        documentTypes: ['audit_report'],
        estimatedTime: '30',
        isAvailable: true,
        isSelected: true // Default selected
      },
      {
        id: 'company_format',
        name: 'Address to Members Format',
        description: 'Verifies "To the Members of [Company Name]" format',
        category: 'Basic Compliance',
        documentTypes: ['audit_report'],
        estimatedTime: '30',
        isAvailable: true,
        isSelected: true
      },
      {
        id: 'profit_loss_consistency',
        name: 'Profit/Loss Opinion Consistency',
        description: 'Matches profit/loss in opinion with your selection',
        category: 'Financial Results Compliance',
        documentTypes: ['audit_report'],
        estimatedTime: '45',
        isAvailable: true,
        isSelected: true
      },
      {
        id: 'signature_date',
        name: 'PKF Signature Block & Date',
        description: 'Verifies complete signature block with correct date',
        category: 'Basic Compliance',
        documentTypes: ['audit_report'],
        estimatedTime: '30',
        isAvailable: true,
        isSelected: true
      },
      {
        id: 'key_audit_matter',
        name: 'Key Audit Matters Disclosure',
        description: 'Ensures at least one Key Audit Matter is present',
        category: 'Audit Standards',
        documentTypes: ['audit_report'],
        estimatedTime: '30',
        isAvailable: true,
        isSelected: false // Optional by default
      },
      {
        id: 'audit_trail_software',
        name: 'Audit Trail Software Disclosure',
        description: 'Checks for accounting software audit trail mention',
        category: 'Technology Compliance',
        documentTypes: ['audit_report'],
        estimatedTime: '30',
        isAvailable: true,
        isSelected: false
      },
      {
        id: 'section_197_reference',
        name: 'Section 197(16) Reference',
        description: 'Verifies Section 197(16) director remuneration disclosure',
        category: 'Regulatory Compliance',
        documentTypes: ['audit_report'],
        estimatedTime: '30',
        isAvailable: true,
        isSelected: false
      },
      {
        id: 'company_name_consistency',
        name: 'Company Name Consistency',
        description: 'Checks for consistent company naming throughout',
        category: 'Basic Compliance',
        documentTypes: ['audit_report'],
        estimatedTime: '30',
        isAvailable: true,
        isSelected: true
      }
    ].forEach(addCheck);

    // Conditional audit report checks
    if (parameters.audit_report_type !== 'Normal') {
      addCheck({
        id: 'financial_statements_type',
        name: `${parameters.audit_report_type} Financial Statements Consistency`,
        description: `Verifies consistent use of "${parameters.audit_report_type}" throughout`,
        category: 'Statement Type Compliance',
        documentTypes: ['audit_report'],
        estimatedTime: '45',
        isAvailable: true,
        isSelected: true
      });
    }

    // *** CRITICAL: ADD THE CTRL+F CHECK ***
    if (parameters.audit_report_type === 'Standalone' || parameters.audit_report_type === 'Consolidated') {
      addCheck({
        id: 'financial_statement_type_check',
        name: `${parameters.audit_report_type} Financial Statement Type Verification`,
        description: `Ctrl+F search to count instances of ${parameters.audit_report_type} financial statements`,
        category: 'Statement Type Compliance',
        documentTypes: ['audit_report'],
        estimatedTime: '45',
        isAvailable: true,
        isSelected: true // Default selected
      });
    }

        // *** ADD THE OPINION TYPE CHECK ***
    if (parameters.audit_opinion_type === 'Adverse' || parameters.audit_opinion_type === 'Qualified') {
      addCheck({
        id: 'audit_opinion_type_check',
        name: `${parameters.audit_opinion_type} Opinion Type Verification`,
        description: `Verifies instances of ${parameters.audit_opinion_type} opinion in the audit report`,
        category: 'Opinion Type Compliance',
        documentTypes: ['audit_report'],
        estimatedTime: '45',
        isAvailable: true,
        isSelected: true // Default selected since it's conditional
      });
    }

    if (parameters.company_listing_status === 'Listed') {
      addCheck({
        id: 'brsr_brr',
        name: parameters.top_1000_or_500 === 'Yes' ? 'BRSR Disclosure Check' : 'BRR Disclosure Check',
        description: parameters.top_1000_or_500 === 'Yes'
          ? 'Verifies Business Responsibility and Sustainability Report disclosure'
          : 'Verifies Business Responsibility Report disclosure',
        category: 'Sustainability Reporting',
        documentTypes: ['audit_report'],
        estimatedTime: '45',
        isAvailable: true,
        isSelected: true
      });
    }

    if (parameters.audit_report_type === 'Consolidated') {
      addCheck({
        id: 'consolidated_wording',
        name: 'Consolidated Statements Wording Consistency',
        description: 'Verifies consistent use of "consolidated" throughout documents',
        category: 'Consolidated Compliance',
        documentTypes: ['audit_report'],
        estimatedTime: '45',
        isAvailable: true,
        isSelected: true
      });
    }
  }

  // ===================================================================
  // CARO ANNEXURE CHECKS (SINGLE DOCUMENT)
  // ===================================================================
  if (documents.annexure_a) {
    if (parameters.audit_report_type === 'Consolidated') {
      addCheck({
        id: 'clause_21',
        name: 'CARO Clause (xxi) - Consolidated Only',
        description: 'Verifies only clause (xxi) is present for consolidated reports',
        category: 'CARO Consolidated',
        documentTypes: ['annexure_a'],
        estimatedTime: '60',
        isAvailable: true,
        isSelected: true
      });
    } else if (parameters.audit_report_type === 'Standalone' || parameters.audit_report_type === 'Normal') {
      [
        {
          id: 'clause_20',
          name: 'CARO Clauses (i-xx) - Standalone Complete',
          description: 'Verifies all 20 mandatory clauses (i) to (xx) are present',
          category: 'CARO Standalone',
          documentTypes: ['annexure_a'],
          estimatedTime: '90',
          isAvailable: true,
          isSelected: false
        },
        {
          id: 'benami_property_clause',
          name: 'CARO Clause (i)(e) - Benami Property',
          description: 'Verifies Benami Property proceedings disclosure',
          category: 'CARO Regulatory Compliance',
          documentTypes: ['annexure_a'],
          estimatedTime: '45',
          isAvailable: true,
          isSelected: false
        },
        {
          id: 'caro_clause_xiii_related_party',
          name: 'CARO Clause (xiii) - Related Party Transactions',
          description: 'Verifies Section 177 & 188 compliance disclosure',
          category: 'Related Party Compliance',
          documentTypes: ['annexure_a'],
          estimatedTime: '45',
          isAvailable: true,
          isSelected: false
        },
        {
          id: 'caro_clause_vii_a_statutory_dues',
          name: 'CARO Clause (vii)(a) - Regular Statutory Dues',
          description: 'Verifies regular deposit of undisputed statutory dues',
          category: 'Statutory Dues Compliance',
          documentTypes: ['annexure_a'],
          estimatedTime: '45',
          isAvailable: true,
          isSelected: false
        },
        {
          id: 'caro_clause_vii_b_disputed_dues',
          name: 'CARO Clause (vii)(b) - Disputed Statutory Dues',
          description: 'Verifies disputed statutory dues disclosure',
          category: 'Statutory Dues Compliance',
          documentTypes: ['annexure_a'],
          estimatedTime: '45',
          isAvailable: true,
          isSelected: false
        }
      ].forEach(addCheck);

      // Conditional CARO checks based on parameters
      if (parameters.has_cost_auditor === 'Yes') {
        addCheck({
          id: 'caro_clause_vi_cost_auditor',
          name: 'CARO Clause (vi) - Cost Records Maintenance',
          description: 'Verifies cost records maintenance under Section 148',
          category: 'Cost Audit Compliance',
          documentTypes: ['annexure_a'],
          estimatedTime: '45',
          isAvailable: true,
          isSelected: true
        });
      }

      

      if (parameters.has_internal_auditor === 'Yes') {
        addCheck({
          id: 'internal_auditor_clause_xiv',
          name: 'CARO Clause (xiv) - Internal Auditor',
          description: 'Verifies internal auditor appointment disclosure',
          category: 'Internal Auditor Compliance',
          documentTypes: ['annexure_a'],
          estimatedTime: '45',
          isAvailable: true,
          isSelected: true
        });
      }

      if (parameters.is_nbfc === 'Yes') {
        addCheck({
          id: 'caro_nbfc_exemptions',
          name: 'NBFC CARO Clause Exemptions',
          description: 'Verifies proper NBFC clause exemptions in CARO',
          category: 'NBFC Compliance',
          documentTypes: ['annexure_a'],
          estimatedTime: '45',
          isAvailable: true,
          isSelected: true
        });
      }
    }
  }

  // ===================================================================
  // MULTI-DOCUMENT CHECKS - CROSS-REFERENCE COMPLIANCE
  // ===================================================================

  // Audit Report + CARO Reference Checks
  if (documents.audit_report && documents.annexure_a) {
    addCheck({
      id: 'audit_report_annexure_a_reference',
      name: 'Audit Report + CARO Reference Matching',
      description: 'Cross-verifies paragraph number references between Audit Report and CARO',
      category: 'Cross-Reference Compliance',
      documentTypes: ['audit_report', 'annexure_a'],
      estimatedTime: '60',
      isAvailable: true,
      isSelected: false
    });
  }

  // Audit Report + IFC Reference Checks
  if (documents.audit_report && documents.annexure_b) {
    addCheck({
      id: 'audit_report_annexure_b_reference',
      name: 'Audit Report + IFC Reference Matching',
      description: 'Cross-verifies paragraph references between Audit Report and IFC Annexure B',
      category: 'Cross-Reference Compliance',
      documentTypes: ['audit_report', 'annexure_b'],
      estimatedTime: '60',
      isAvailable: true,
      isSelected: false
    });
  }

  // ===================================================================
  // NOTES + CARO COMPLIANCE CHECKS (2-Document)
  // ===================================================================
  if (documents.notes && documents.annexure_a &&
      (parameters.audit_report_type === 'Standalone' || parameters.audit_report_type === 'Normal')) {

    [
      {
        id: 'enhanced_notes_caro_fixed_deposits',
        name: 'Notes + CARO Fixed Deposits Direct Analysis',
        description: 'Comprehensive search for fixed deposits in Notes borrowings and CARO verification',
        category: 'Enhanced Fixed Deposits Compliance Alignment',
        documentTypes: ['notes', 'annexure_a'],
        estimatedTime: '90',
        isAvailable: true,
        isSelected: false
      },
      {
        id: 'inventory_goods_in_transit_check',
        name: 'Notes + CARO Goods in Transit Direct Check',
        description: 'Comprehensive search for goods in transit across all Notes and CARO verification',
        category: 'Inventory Goods in Transit Compliance',
        documentTypes: ['notes', 'annexure_a'],
        estimatedTime: '75',
        isAvailable: true,
        isSelected:  false
      },
      {
        id: 'enhanced_inventory_writeoff_check',
        name: 'Notes + CARO Inventory Write-off Direct Check',
        description: 'Comprehensive search for inventory write-offs across all Notes and CARO verification',
        category: 'Enhanced Inventory Write-off Compliance',
        documentTypes: ['notes', 'annexure_a'],
        estimatedTime: '75',
        isAvailable: true,
        isSelected: false
      },
      {
        id: 'enhanced_secured_borrowings_quarterly_returns_check',
        name: 'Notes + CARO Secured Borrowings Direct Check',
        description: 'Comprehensive search for secured borrowings > 5 crores and CARO verification',
        category: 'Enhanced Secured Borrowings Quarterly Returns Compliance',
        documentTypes: ['notes', 'annexure_a'],
        estimatedTime: '75',
        isAvailable: true,
        isSelected: false
      },

      {
        id: 'notes_ppe_caro_immovable_property_alignment',
        name: 'Notes PPE Immovable Property + CARO Clause (i)(c) Title Deeds Alignment',
        description: 'Check if immovable properties (Land/Buildings) in PPE notes align with CARO clause (i)(c) title deeds compliance',
        category: 'PPE Immovable Property Compliance',
        documentTypes: ['notes', 'annexure_a'],
        estimatedTime: '75',
        isAvailable: true,
        isSelected: false

      },
      {
        id: 'notes_caro_related_party_loans_alignment',
        name: 'Notes + CARO Related Party Loans Alignment',
        description: 'Cross-verifies related party loan amounts between Notes and CARO clauses',
        category: 'Related Party Loans Alignment',
        documentTypes: ['notes', 'annexure_a'],
        estimatedTime: '105',
        isAvailable: true,
        isSelected: true
      },
      {
        id: 'notes_caro_new_investments_loans',
        name: 'Notes + CARO New Investments/Loans Alignment',
        description: 'Cross-verifies new investments, loans, and guarantees with CARO clause (iii)',
        category: 'New Activities Compliance',
        documentTypes: ['notes', 'annexure_a'],
        estimatedTime: '90',
        isAvailable: true,
        isSelected: false
      },
      {
        id: 'notes_caro_doubtful_loans',
        name: 'Notes + CARO Doubtful Loans Recovery Assessment',
        description: 'Cross-verifies provision for doubtful loans with CARO clause (iii)(b)',
        category: 'Doubtful Loans Compliance',
        documentTypes: ['notes', 'annexure_a'],
        estimatedTime: '90',
        isAvailable: true,
        isSelected: false
      },
      {
        id: 'notes_caro_aggregate_capital',
        name: 'Notes + CARO Aggregate Activities vs Capital',
        description: 'Checks if aggregate activities exceed 60% of capital and verifies CARO clause (iv)',
        category: 'Capital Adequacy Compliance',
        documentTypes: ['notes', 'annexure_a'],
        estimatedTime: '90',
        isAvailable: true,
        isSelected: false
      },
      {
        id: 'notes_caro_statutory_dues_static',
        name: 'Notes + CARO Static Statutory Dues Analysis',
        description: 'Checks for static statutory dues and verifies CARO clause (vii)(a)',
        category: 'Statutory Dues Compliance',
        documentTypes: ['notes', 'annexure_a'],
        estimatedTime: '90',
        isAvailable: true,
        isSelected: false
      },
      {
        id: 'notes_caro_capital_debt_increase',
        name: 'Notes + CARO Capital/Debt Increase Verification',
        description: 'Checks for capital or debt increases and verifies CARO clause (x)(a)',
        category: 'Capital Raising Compliance',
        documentTypes: ['notes', 'annexure_a'],
        estimatedTime: '90',
        isAvailable: true,
        isSelected: false
      },
      {
        id: 'notes_caro_rpt_minority_approval',
        name: 'Notes + CARO RPT Minority Approval Check',
        description: 'Checks if RPT exceeds 10% of turnover and verifies CARO clause (xiii)',
        category: 'Related Party Transactions Compliance',
        documentTypes: ['notes', 'annexure_a'],
        estimatedTime: '90',
        isAvailable: true,
        isSelected: false
      },
      {
        id: 'notes_caro_immovable_property_disputes',
        name: 'Notes + CARO Immovable Property Disputes Alignment',
        description: 'Cross-verifies immovable property disputes between Notes and CARO clause (i)(c)',
        category: 'Property Disputes Alignment',
        documentTypes: ['notes', 'annexure_a'],
        estimatedTime: '75',
        isAvailable: true,
        isSelected: false
      },
      {
        id: 'notes_caro_revaluation_reserve_check',
        name: 'Notes + CARO Revaluation Reserve Compliance',
        description: 'Checks for revaluation reserve changes in Notes Other Equity and verifies CARO clause (i)(d)',
        category: 'Revaluation Reserve Compliance',
        documentTypes: ['notes', 'annexure_a'],
        estimatedTime: '60',
        isAvailable: true,
        isSelected: false
      },
    ].forEach(addCheck);
  }

  // ===================================================================
  // BALANCE SHEET + CARO ASSET VERIFICATION CHECKS
  // ===================================================================
  if (documents.balance_sheet && documents.annexure_a &&
      (parameters.audit_report_type === 'Standalone' || parameters.audit_report_type === 'Normal')) {

    [
      {
        id: 'bs_caro_ppe_assets',
        name: 'Balance Sheet + CARO PPE Assets Verification',
        description: 'If PPE > 0, verifies CARO clauses (i)(a)(A) & (i)(b) are present',
        category: 'Asset Disclosure Alignment',
        documentTypes: ['balance_sheet', 'annexure_a'],
        estimatedTime: '75',
        isAvailable: true,
        isSelected: false
      },
      {
        id: 'bs_caro_intangible_assets',
        name: 'Balance Sheet + CARO Intangible Assets Check',
        description: 'If intangible assets > 0, verifies CARO clause (i)(a)(B)',
        category: 'Asset Disclosure Alignment',
        documentTypes: ['balance_sheet', 'annexure_a'],
        estimatedTime: '75',
        isAvailable: true,
        isSelected: false
      },
      {
        id: 'bs_caro_current_ratio_assets',
        name: 'Balance Sheet + CARO Current Ratio Analysis',
        description: 'Analyzes current assets vs liabilities and verifies CARO clause (ix)(d)',
        category: 'Liquidity & Fund Utilization Alignment',
        documentTypes: ['balance_sheet', 'annexure_a'],
        estimatedTime: '90',
        isAvailable: true,
        isSelected: false
      }
    ].forEach(addCheck);
  }

  // ===================================================================
  // CSR + CARO CHECKS
  // ===================================================================
  if (documents.csr_notes && documents.annexure_a &&
      (parameters.audit_report_type === 'Standalone' || parameters.audit_report_type === 'Normal')) {
    addCheck({
      id: 'csr_caro_unspent_amount',
      name: 'CSR Notes + CARO Unspent Amount Alignment',
      description: 'Checks CSR unspent amount and verifies appropriate CARO clause (xx) disclosure',
      category: 'CSR Compliance Alignment',
      documentTypes: ['csr_notes', 'annexure_a'],
      estimatedTime: '75',
      isAvailable: true,
      isSelected: false
    });
  }

  // ===================================================================
  // SECRETARIAL REPORT + CARO CHECKS
  // ===================================================================
  if (documents.sec_report && documents.annexure_a &&
      (parameters.audit_report_type === 'Standalone' || parameters.audit_report_type === 'Normal')) {
    addCheck({
      id: 'secretarial_caro_comprehensive',
      name: 'Comprehensive Secretarial + CARO Multi-Section Alignment',
      description: 'Cross-verifies all key regulatory sections between Secretarial Audit and CARO',
      category: 'Comprehensive Regulatory Cross-Compliance',
      documentTypes: ['sec_report', 'annexure_a'],
      estimatedTime: '150',
      isAvailable: true,
      isSelected: false
    });
  }

  // ===================================================================
  // MULTI-DOCUMENT CONSISTENCY CHECKS
  // ===================================================================
  if (documents.audit_report && documents.balance_sheet && documents.notes) {
    addCheck({
      id: 'audit_bs_notes_consistency',
      name: 'Audit Report + Balance Sheet + Notes Consistency',
      description: 'Verifies consistency of company information across primary financial documents',
      category: 'Financial Statement Consistency',
      documentTypes: ['audit_report', 'balance_sheet', 'notes'],
      estimatedTime: '90',
      isAvailable: true,
      isSelected: false
    });
  }

  // ===================================================================
  // NBFC MULTI-DOCUMENT CHECKS
  // ===================================================================
  if (parameters.is_nbfc === 'Yes' && documents.audit_report && documents.annexure_a && documents.balance_sheet) {
    addCheck({
      id: 'nbfc_specific_compliance',
      name: 'NBFC Specific Multi-Document Compliance',
      description: 'NBFC-specific compliance verification across multiple documents',
      category: 'NBFC Compliance',
      documentTypes: ['audit_report', 'annexure_a', 'balance_sheet'],
      estimatedTime: '120',
      isAvailable: true,
      isSelected: false
    });
  }

  // ===================================================================
  // ANNUAL REPORT + CARO CROSS-COMPLIANCE CHECKS
  // ===================================================================
  if (documents.annual_report && documents.annexure_a &&
      (parameters.audit_report_type === 'Standalone' || parameters.audit_report_type === 'Normal')) {

    [
      {
        id: 'annual_report_caro_income_tax',
        name: 'Annual Report + CARO Income Tax Cross-Check',
        description: 'Cross-verifies income tax raids disclosure between Annual Report and CARO',
        category: 'Annual Report Cross-Compliance',
        documentTypes: ['annual_report', 'annexure_a'],
        estimatedTime: '90',
        isAvailable: true,
        isSelected: false
      },
      {
        id: 'annual_report_caro_defaults',
        name: 'Annual Report + CARO Defaults Cross-Check',
        description: 'Cross-verifies defaults/wilful defaulter disclosure between documents',
        category: 'Annual Report Cross-Compliance',
        documentTypes: ['annual_report', 'annexure_a'],
        estimatedTime: '90',
        isAvailable: true,
        isSelected: false
      },
      {
        id: 'annual_report_caro_rights_issue',
        name: 'Annual Report + CARO Rights Issue Cross-Check',
        description: 'Cross-verifies rights issue disclosure between Annual Report and CARO',
        category: 'Annual Report Cross-Compliance',
        documentTypes: ['annual_report', 'annexure_a'],
        estimatedTime: '90',
        isAvailable: true,
        isSelected: false
      },
      {
        id: 'annual_report_caro_fraud',
        name: 'Annual Report + CARO Fraud Cross-Check',
        description: 'Cross-verifies fraud disclosure between Annual Report and CARO',
        category: 'Annual Report Cross-Compliance',
        documentTypes: ['annual_report', 'annexure_a'],
        estimatedTime: '90',
        isAvailable: true,
        isSelected: false
      },
      {
        id: 'annual_report_caro_whistleblower',
        name: 'Annual Report + CARO Whistle-blower Cross-Check',
        description: 'Cross-verifies whistle-blower disclosure between Annual Report and CARO',
        category: 'Annual Report Cross-Compliance',
        documentTypes: ['annual_report', 'annexure_a'],
        estimatedTime: '90',
        isAvailable: true,
        isSelected: false
      },
      {
        id: 'annual_report_caro_cost_records',
        name: 'Annual Report + CARO Cost Records Cross-Check',
        description: 'Cross-verifies cost records/auditor disclosure between Annual Report and CARO',
        category: 'Annual Report Cross-Compliance',
        documentTypes: ['annual_report', 'annexure_a'],
        estimatedTime: '90',
        isAvailable: true,
        isSelected: false
      }
    ].forEach(addCheck);
  }

  console.log(`🔍 Generated ${availableChecks.length} unique checks from ${addedCheckIds.size} unique IDs`);
  console.log(`🔍 Looking for financial_statement_type_check:`, 
    availableChecks.find(c => c.id === 'financial_statement_type_check'));

  return availableChecks;
}

// ===================================================================
// GROUP CHECKS BY CATEGORY
// ===================================================================

function groupChecksByCategory(checks: CheckOption[]): CheckCategory[] {
  const categories: Record<string, CheckOption[]> = {};

  checks.forEach(check => {
    if (!categories[check.category]) {
      categories[check.category] = [];
    }
    categories[check.category].push(check);
  });

  return Object.entries(categories).map(([name, checks]) => ({
    name,
    checks,
    totalSelected: checks.filter(c => c.isSelected).length,
    totalAvailable: checks.length
  }));
}

// ===================================================================
// CHECK SELECTION COMPONENT
// ===================================================================

interface CheckSelectionProps {
  documents: DocumentFiles;
  parameters: AnalysisParameters;
  onSelectionChange: (selectedChecks: string[]) => void;
  onStartAnalysis: () => void;
  onGoBack: () => void;
  isProcessing: boolean;
}

const CheckSelection: React.FC<CheckSelectionProps> = ({
  documents,
  parameters,
  onSelectionChange,
  onStartAnalysis,
  onGoBack,
  isProcessing
}) => {
  const [availableChecks, setAvailableChecks] = useState<CheckOption[]>(() =>
    getAvailableChecksForSelection(documents, parameters)
  );
  const [categories, setCategories] = useState<CheckCategory[]>(() =>
    groupChecksByCategory(getAvailableChecksForSelection(documents, parameters))
  );

  const handleCheckToggle = (checkId: string, checked: boolean) => {
    const updatedChecks = availableChecks.map(check =>
      check.id === checkId ? { ...check, isSelected: checked } : check
    );
    setAvailableChecks(updatedChecks);
    setCategories(groupChecksByCategory(updatedChecks));

    const selectedIds = updatedChecks.filter(c => c.isSelected).map(c => c.id);
    onSelectionChange(selectedIds);
  };

  const handleCategoryToggle = (categoryName: string, checked: boolean) => {
    const updatedChecks = availableChecks.map(check =>
      check.category === categoryName ? { ...check, isSelected: checked } : check
    );
    setAvailableChecks(updatedChecks);
    setCategories(groupChecksByCategory(updatedChecks));

    const selectedIds = updatedChecks.filter(c => c.isSelected).map(c => c.id);
    onSelectionChange(selectedIds);
  };

  const totalSelected = availableChecks.filter(c => c.isSelected).length;
  const estimatedTimeSeconds = availableChecks
    .filter(c => c.isSelected)
    .reduce((acc, check) => acc + parseInt(check.estimatedTime), 0);

  return (
    <div className="space-y-6">
      <Card className="border-blue-200 bg-blue-50">
        <CardHeader>
          <CardTitle className="text-blue-800 flex items-center gap-2">
            <Settings className="h-5 w-5" />
            📋 Select Compliance Checks to Run
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">{totalSelected}</div>
              <div className="text-sm text-gray-600">Checks Selected</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">{availableChecks.length}</div>
              <div className="text-sm text-gray-600">Total Available</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600 flex items-center justify-center gap-1">
                <Clock className="h-5 w-5" />
                {Math.ceil(estimatedTimeSeconds / 60)}m
              </div>
              <div className="text-sm text-gray-600">Est. Time</div>
            </div>
          </div>

          <Alert className="mt-4">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              <strong>💡 Tip:</strong> Essential compliance checks are pre-selected. You can customize by selecting/deselecting specific checks or entire categories.
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>

      {categories.map(category => (
        <Card key={category.name}>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <Checkbox
                  checked={category.totalSelected === category.totalAvailable}
                  onCheckedChange={(checked) => handleCategoryToggle(category.name, !!checked)}
                  disabled={isProcessing}
                />
                <CardTitle className="text-lg">{category.name}</CardTitle>
                <Badge variant={category.totalSelected > 0 ? "default" : "outline"}>
                  {category.totalSelected}/{category.totalAvailable}
                </Badge>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {category.checks.map(check => (
                <div key={check.id} className={cn(
                  "flex items-start space-x-3 p-3 border rounded-lg transition-colors",
                  check.isSelected ? "bg-blue-50 border-blue-200" : "hover:bg-gray-50"
                )}>
                  <Checkbox
                    checked={check.isSelected}
                    onCheckedChange={(checked) => handleCheckToggle(check.id, !!checked)}
                    disabled={isProcessing}
                  />
                  <div className="flex-1">
                    <div className="flex items-center justify-between">
                      <h4 className="font-medium">{check.name}</h4>
                      <Badge variant="secondary" className="flex items-center gap-1">
                        <Clock className="h-3 w-3" />
                        {check.estimatedTime}s
                      </Badge>
                    </div>
                    <p className="text-sm text-gray-600 mt-1">{check.description}</p>
                    <div className="flex items-center space-x-2 mt-2">
                      {check.documentTypes.map(docType => (
                        <Badge key={docType} variant="outline" className="text-xs">
                          {docType.replace('_', ' ')}
                        </Badge>
                      ))}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      ))}

      <div className="flex justify-between space-x-4">
        <Button
          variant="outline"
          onClick={onGoBack}
          disabled={isProcessing}
        >
          ← Back to Form
        </Button>

        <Button
          onClick={onStartAnalysis}
          disabled={isProcessing || totalSelected === 0}
          size="lg"
          className="flex-1 md:flex-none"
        >
          {isProcessing ? (
            <span className="flex items-center">
              <div className="animate-spin h-4 w-4 border-2 border-white border-t-transparent rounded-full mr-2"></div>
              Running {totalSelected} Checks...
            </span>
          ) : (
            `🚀 Run ${totalSelected} Selected Checks (≈${Math.ceil(estimatedTimeSeconds / 60)}m)`
          )}
        </Button>
      </div>
    </div>
  );
};

// ===================================================================
// MAIN ANALYZER COMPONENT
// ===================================================================

const Analyzer = () => {
  const { currentUser } = useAuth();
  const { toast } = useToast();
  const navigate = useNavigate();

  const [analysisComplete, setAnalysisComplete] = useState(false);
  const [analysisData, setAnalysisData] = useState<AnalysisData | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [showCheckSelection, setShowCheckSelection] = useState(false);
  const [selectedCheckIds, setSelectedCheckIds] = useState<string[]>([]);
  const [processingProgress, setProcessingProgress] = useState<ProcessingProgress>({
    completed: 0,
    total: 0,
    currentCheck: "Ready to start...",
    phase: 'single-document'
  });

  // Form state
  const [parameters, setParameters] = useState<AnalysisParameters>({
    company_name: "",
    audit_date: new Date(),
    profit_or_loss: "Profit",
    company_listing_status: "Unlisted",
    top_1000_or_500: "No",
    audit_report_type: "Normal",
    audit_opinion_type: "Unmodified",
    is_nbfc: "No",
    has_internal_auditor: "No",
    has_cost_auditor: "No",
    related_party_note_number: "",
  });

  // Document files state
  const [documents, setDocuments] = useState<DocumentFiles>({});

  // Form validation - at least one document and company name
  const uploadedDocuments = Object.values(documents).filter(file => file !== undefined);
  const isFormValid = parameters.company_name.trim().length > 0 && uploadedDocuments.length > 0;

  // Handle parameter changes
  const handleParameterChange = (name: keyof AnalysisParameters, value: any) => {
    setParameters(prev => {
      const updated = { ...prev, [name]: value };

      // Auto-reset dependent fields
      if (name === "company_listing_status" && value === "Unlisted") {
        updated.top_1000_or_500 = "No";
      }

      return updated;
    });
  };

  // Handle file uploads
  const handleFileUpload = (documentType: keyof DocumentFiles, event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];

    if (file) {
      // Basic validation
      if (file.type !== 'application/pdf') {
        toast({
          title: "Invalid File Type",
          description: `Please upload a PDF file for ${documentType}`,
          variant: "destructive",
        });
        event.target.value = '';
        return;
      }

      if (file.size > 100 * 1024 * 1024) { // 100MB limit
        toast({
          title: "File Too Large",
          description: `File size should be less than 100MB for ${documentType}`,
          variant: "destructive",
        });
        event.target.value = '';
        return;
      }
    }

    setDocuments(prev => ({
      ...prev,
      [documentType]: file || undefined
    }));

    event.target.value = '';
  };

  // Remove uploaded file
  const removeFile = (documentType: keyof DocumentFiles) => {
    setDocuments(prev => {
      const updated = { ...prev };
      delete updated[documentType];
      return updated;
    });
  };

  // File upload component
  const FileUploader = ({
    documentType,
    label,
    required = false
  }: {
    documentType: keyof DocumentFiles;
    label: string;
    required?: boolean;
  }) => {
    const file = documents[documentType];

    return (
      <div className="space-y-2">
        <Label className="flex items-center gap-2">
          {label}
          {required && <span className="text-red-500">*</span>}
        </Label>

        {file ? (
          <div className="border-2 border-green-300 bg-green-50 rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <FileText className="h-8 w-8 text-green-500" />
                <div>
                  <p className="text-sm font-medium text-green-700">{file.name}</p>
                  <p className="text-xs text-green-600">
                    {(file.size / 1024 / 1024).toFixed(2)} MB
                  </p>
                </div>
              </div>
              <Button
                type="button"
                variant="ghost"
                size="sm"
                onClick={() => removeFile(documentType)}
                disabled={isProcessing}
                className="text-red-500 hover:text-red-700"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          </div>
        ) : (
          <div className="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center hover:border-gray-400 transition-colors">
            <input
              type="file"
              accept=".pdf"
              onChange={(e) => handleFileUpload(documentType, e)}
              className="hidden"
              id={`file-${documentType}`}
              disabled={isProcessing}
            />
            <label htmlFor={`file-${documentType}`} className="cursor-pointer">
              <div className="space-y-2">
                <Upload className="h-8 w-8 mx-auto text-gray-400" />
                <p className="text-sm text-gray-600">Click to upload PDF</p>
                <p className="text-xs text-gray-400">Maximum 100MB</p>
              </div>
            </label>
          </div>
        )}
      </div>
    );
  };

  // Handle showing check selection
  const handleShowCheckSelection = () => {
    if (!isFormValid) {
      toast({
        title: "Complete Required Fields",
        description: "Please fill in company name and upload at least one document first",
        variant: "destructive",
      });
      return;
    }

    // Initialize selected checks with defaults
    const availableChecks = getAvailableChecksForSelection(documents, parameters);
    const defaultSelectedIds = availableChecks.filter(check => check.isSelected).map(check => check.id);
    setSelectedCheckIds(defaultSelectedIds);
    setShowCheckSelection(true);

    console.log(`🔍 Available checks: ${availableChecks.length}`);
    console.log(`📋 Default selected: ${defaultSelectedIds.length}`);
  };

  // Handle analysis start
  const handleStartAnalysis = async () => {
    console.log('=== SELECTIVE ANALYSIS STARTED ===');
    console.log('Selected check IDs:', selectedCheckIds);

    if (!currentUser) {
      toast({
        title: "Authentication Required",
        description: "Please log in to perform analysis",
        variant: "destructive",
      });
      return;
    }

    if (selectedCheckIds.length === 0) {
      toast({
        title: "No Checks Selected",
        description: "Please select at least one check to run",
        variant: "destructive",
      });
      return;
    }

    setIsProcessing(true);
    setAnalysisComplete(false);
    setAnalysisData(null);
    setProcessingProgress({
      completed: 0,
      total: selectedCheckIds.length,
      currentCheck: "Initializing selective analysis...",
      phase: 'single-document'
    });

    toast({
      title: "Starting Selective Analysis",
      description: `Processing ${selectedCheckIds.length} selected checks...`,
    });

    try {
      console.log("Starting selective document processing...");

      // Process documents with selected checks only
      const results = await processDocumentsForAnalysis(
        documents,
        parameters,
        selectedCheckIds, // Pass selected check IDs
        (progress) => {
          console.log(`Progress: ${progress.completed}/${progress.total} - ${progress.currentCheck}`);
          setProcessingProgress(progress);
        }
      );

      console.log("Selective document processing completed:", Object.keys(results));

      // Generate summary for results
      const summary = getAnalysisSummary(results);
      console.log("Selective analysis summary:", summary);

      // Try to save to Firebase
      let analysisId = `selective_${Date.now()}`;

      try {
        console.log("Attempting to save selective analysis to Firebase...");
        analysisId = await saveAnalysisResults(
          currentUser.uid,
          parameters,
          results,
          documents,
          {
            total: summary.total,
            compliant: summary.compliant,
            nonCompliant: summary.nonCompliant,
            compliancePercentage: summary.compliancePercentage
          }
        );
        console.log("Successfully saved selective analysis with ID:", analysisId);

        toast({
          title: "Selective Analysis Complete",
          description: `${selectedCheckIds.length} checks completed with ${summary.compliancePercentage}% compliance rate`,
        });
      } catch (saveError) {
        console.error("Failed to save to Firebase:", saveError);

        toast({
          title: "Analysis Complete (Save Warning)",
          description: `${selectedCheckIds.length} checks completed with ${summary.compliancePercentage}% compliance rate, but could not save to database`,
        });
      }

      // Create analysis data for display
      const analysisData: AnalysisData = {
        id: analysisId,
        parameters,
        results: results,
        documents,
        timestamp: Date.now(),
        summary: {
          total: summary.total,
          compliant: summary.compliant,
          nonCompliant: summary.nonCompliant,
          compliancePercentage: summary.compliancePercentage
        }
      };

      setAnalysisData(analysisData);
      setAnalysisComplete(true);
      setShowCheckSelection(false); // Hide check selection after completion

      console.log('=== SELECTIVE ANALYSIS COMPLETED SUCCESSFULLY ===');

    } catch (error) {
      console.error("Error during selective analysis:", error);
      toast({
        title: "Analysis Failed",
        description: `An error occurred: ${error instanceof Error ? error.message : String(error)}`,
        variant: "destructive",
      });
    } finally {
      setIsProcessing(false);
    }
  };

  // Start a new analysis
  const handleStartNewAnalysis = () => {
    console.log('Starting new analysis...');
    setAnalysisComplete(false);
    setAnalysisData(null);
    setIsProcessing(false);
    setShowCheckSelection(false);
    setSelectedCheckIds([]);
    setProcessingProgress({
      completed: 0,
      total: 0,
      currentCheck: "Ready to start...",
      phase: 'single-document'
    });
  };

  // Go back to form from check selection
  const handleGoBackToForm = () => {
    setShowCheckSelection(false);
    setSelectedCheckIds([]);
  };

  // Format progress display
  const getProgressPercentage = () => {
    if (processingProgress.total === 0) return 0;
    return Math.round((processingProgress.completed / processingProgress.total) * 100);
  };

  const formatCheckName = (check: string) => {
    if (check === "Analysis complete") return check;
    if (check.includes("...")) return check;

    // Convert snake_case to Title Case
    return check
      .replace(/_/g, ' ')
      .split(' ')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">Audit Report Analyzer</h1>
        {analysisComplete && (
          <Button variant="outline" onClick={handleStartNewAnalysis}>
            Start New Analysis
          </Button>
        )}
      </div>

      {!analysisComplete ? (
        <>
          {/* SHOW CHECK SELECTION OR FORM */}
          {showCheckSelection ? (
            <div className="bg-white rounded-lg shadow-sm p-6">
              <CheckSelection
                documents={documents}
                parameters={parameters}
                onSelectionChange={setSelectedCheckIds}
                onStartAnalysis={handleStartAnalysis}
                onGoBack={handleGoBackToForm}
                isProcessing={isProcessing}
              />
            </div>
          ) : (
            <div className="bg-white rounded-lg shadow-sm p-6">
              <div className="mb-6">
                <h2 className="text-xl font-semibold mb-2">Upload Documents for Analysis</h2>
                <p className="text-gray-600">
                  Fill in the required information and select your audit report files to begin the analysis.
                </p>
              </div>

              {/* Company Information */}
              <Card className="mb-6">
                <CardContent className="pt-6">
                  <h3 className="text-lg font-semibold mb-4">Company Information</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="companyName">Company Name *</Label>
                      <Input
                        id="companyName"
                        type="text"
                        placeholder="Enter Company Name"
                        value={parameters.company_name}
                        onChange={(e) => handleParameterChange("company_name", e.target.value)}
                        required
                        disabled={isProcessing}
                        className={parameters.company_name.trim() ? "border-green-300" : "border-red-300"}
                      />
                    </div>

                    <div>
                      <Label htmlFor="auditDate">Audit Report Date</Label>
                      <Popover>
                        <PopoverTrigger asChild>
                          <Button
                            id="auditDate"
                            variant="outline"
                            className={cn(
                              "w-full justify-start text-left font-normal",
                              !parameters.audit_date && "text-muted-foreground"
                            )}
                            disabled={isProcessing}
                          >
                            <CalendarIcon className="mr-2 h-4 w-4" />
                            {parameters.audit_date ? (
                              format(parameters.audit_date, "PPP")
                            ) : (
                              <span>Pick a date</span>
                            )}
                          </Button>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0" align="start">
                          <Calendar
                            mode="single"
                            selected={parameters.audit_date}
                            onSelect={(date) => handleParameterChange("audit_date", date || new Date())}
                            initialFocus
                            disabled={isProcessing}
                          />
                        </PopoverContent>
                      </Popover>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Analysis Parameters */}
              <Card className="mb-6">
                <CardContent className="pt-6">
                  <h3 className="text-lg font-semibold mb-4">Analysis Parameters</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    <div>
                      <Label>📈 Outcome for the year</Label>
                      <Select
                        value={parameters.profit_or_loss}
                        onValueChange={(value) => handleParameterChange("profit_or_loss", value)}
                        disabled={isProcessing}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="Profit">Profit</SelectItem>
                          <SelectItem value="Loss">Loss</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <Label>🏷️ Company status</Label>
                      <Select
                        value={parameters.company_listing_status}
                        onValueChange={(value) => handleParameterChange("company_listing_status", value)}
                        disabled={isProcessing}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="Unlisted">Unlisted</SelectItem>
                          <SelectItem value="Listed">Listed</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <Label>🖋️ Opinion type</Label>
                      <Select
                        value={parameters.audit_opinion_type}
                        onValueChange={(value) => handleParameterChange("audit_opinion_type", value)}
                        disabled={isProcessing}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="Unmodified">Unmodified</SelectItem>
                          <SelectItem value="Qualified">Qualified</SelectItem>
                          <SelectItem value="Adverse">Adverse</SelectItem>
                          
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <Label>📑 Report variant</Label>
                      <Select
                        value={parameters.audit_report_type}
                        onValueChange={(value) => handleParameterChange("audit_report_type", value)}
                        disabled={isProcessing}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="Normal">Normal</SelectItem>
                          <SelectItem value="Consolidated">Consolidated</SelectItem>
                          <SelectItem value="Standalone">Standalone</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    {parameters.company_listing_status === "Listed" && (
                      <div>
                        <Label>🏆 In Top-1000 / Top-500?</Label>
                        <Select
                          value={parameters.top_1000_or_500}
                          onValueChange={(value) => handleParameterChange("top_1000_or_500", value)}
                          disabled={isProcessing}
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="Yes">Yes</SelectItem>
                            <SelectItem value="No">No</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    )}

                    <div>
                      <Label>🏢 Is the entity an NBFC?</Label>
                      <Select
                        value={parameters.is_nbfc}
                        onValueChange={(value) => handleParameterChange("is_nbfc", value)}
                        disabled={isProcessing}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="No">No</SelectItem>
                          <SelectItem value="Yes">Yes</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <Label>👥 Is Company appointed Internal Auditor? (Mandatory)</Label>
                      <Select
                        value={parameters.has_internal_auditor}
                        onValueChange={(value) => handleParameterChange("has_internal_auditor", value)}
                        disabled={isProcessing}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="No">No</SelectItem>
                          <SelectItem value="Yes">Yes</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <Label>💰 Has Company appointed Cost Auditor?</Label>
                      <Select
                        value={parameters.has_cost_auditor}
                        onValueChange={(value) => handleParameterChange("has_cost_auditor", value)}
                        disabled={isProcessing}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="No">No</SelectItem>
                          <SelectItem value="Yes">Yes</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Document Upload */}
              <Card className="mb-6">
                <CardContent className="pt-6">
                  <h3 className="text-lg font-semibold mb-4">Document Upload</h3>

                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    <FileUploader
                      documentType="audit_report"
                      label="1. Audit Report"
                    />
                    <FileUploader
                      documentType="annexure_a"
                      label="2. Annexure A (CARO)"
                    />
                    <FileUploader
                      documentType="annexure_b"
                      label="3. Annexure B (IFC)"
                    />
                    <FileUploader
                      documentType="balance_sheet"
                      label="4. Balance Sheet"
                    />
                    <FileUploader
                      documentType="notes"
                      label="5. Notes to Accounts"
                    />
                    <FileUploader
                      documentType="pl_notes"
                      label="6. P&L Notes"
                    />
                    <FileUploader
                      documentType="sec_report"
                      label="7. Secretarial Report"
                    />
                    <FileUploader
                      documentType="annual_report"
                      label="8. Annual Report"
                    />
                    <FileUploader
                      documentType="csr_notes"
                      label="9. CSR Notes"
                    />
                  </div>
                </CardContent>
              </Card>

              {/* Submit Button */}
              <div className="flex justify-end space-x-4">
                <Button
                  onClick={handleShowCheckSelection}
                  disabled={isProcessing || !isFormValid}
                  className="w-full md:w-auto"
                  size="lg"
                >
                  {isProcessing ? (
                    <span className="flex items-center">
                      <div className="animate-spin h-4 w-4 border-2 border-white border-t-transparent rounded-full mr-2"></div>
                      Processing...
                    </span>
                  ) : (
                    <span className="flex items-center gap-2">
                      <Settings className="h-4 w-4" />
                      {`Select Checks to Run ${isFormValid ? '✅' : '❌'}`}
                    </span>
                  )}
                </Button>
              </div>

              {/* Help Text */}
              {!isFormValid && (
                <Alert className="mt-4">
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>
                    To proceed:
                    {!parameters.company_name.trim() && " Enter company name."}
                    {uploadedDocuments.length === 0 && " Upload at least one document PDF."}
                  </AlertDescription>
                </Alert>
              )}
            </div>
          )}

          {/* Processing Progress */}
          {isProcessing && (
            <Card className="mt-6">
              <CardContent className="pt-6">
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="font-medium">
                      Processing selected checks: {processingProgress.completed} of {processingProgress.total || 'calculating...'}
                    </div>
                    <div className="text-sm font-medium text-gray-500">
                      {getProgressPercentage()}%
                    </div>
                  </div>

                  <div className="w-full bg-gray-200 rounded-full h-3">
                    <div
                      className="bg-primary h-3 rounded-full transition-all duration-500"
                      style={{ width: `${getProgressPercentage()}%` }}
                    ></div>
                  </div>

                  <div className="flex items-center justify-between text-sm">
                    <div className="flex items-center gap-2">
                      {processingProgress.currentCheck === "Analysis complete" ? (
                        <CheckCircle className="h-4 w-4 text-green-500" />
                      ) : (
                        <div className="animate-spin h-4 w-4 border-2 border-primary border-t-transparent rounded-full"></div>
                      )}
                      <span>
                        {formatCheckName(processingProgress.currentCheck)}
                      </span>
                    </div>
                    <div>
                      <span className="text-xs text-gray-500">
                        {getProgressPercentage() === 100 ? "Complete" : "Processing..."}
                      </span>
                    </div>
                  </div>

                  <div className="text-xs text-gray-500 bg-gray-50 p-2 rounded">
                    {isProcessing ? `Please wait while we analyze your ${selectedCheckIds.length} selected checks...` : "Ready to start"}
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </>
      ) : (
        analysisData && (
          <div className="bg-white rounded-lg shadow-sm p-6">
            <div className="mb-6">
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-xl font-semibold">Selective Analysis Results</h2>
                <div className="flex space-x-2">
                  <Badge variant="outline" className="text-green-600">
                    {selectedCheckIds.length} Checks Run
                  </Badge>
                  <Button
                    variant="outline"
                    onClick={() => navigate(`/dashboard/analysis/${analysisData.id}`)}
                  >
                    View Full Report
                  </Button>
                </div>
              </div>

              {/* Summary Statistics */}
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                <Card>
                  <CardContent className="pt-4">
                    <div className="text-2xl font-bold text-blue-600">{analysisData.summary.total}</div>
                    <div className="text-sm text-gray-500">Checks Run</div>
                  </CardContent>
                </Card>
                <Card>
                  <CardContent className="pt-4">
                    <div className="text-2xl font-bold text-green-600">{analysisData.summary.compliant}</div>
                    <div className="text-sm text-gray-500">Compliant</div>
                  </CardContent>
                </Card>
                <Card>
                  <CardContent className="pt-4">
                    <div className="text-2xl font-bold text-red-600">{analysisData.summary.nonCompliant}</div>
                    <div className="text-sm text-gray-500">Non-Compliant</div>
                  </CardContent>
                </Card>
                <Card>
                  <CardContent className="pt-4">
                    <div className="text-2xl font-bold text-purple-600">{analysisData.summary.compliancePercentage}%</div>
                    <div className="text-sm text-gray-500">Compliance Rate</div>
                  </CardContent>
                </Card>
              </div>

              <p className="text-gray-600 mb-4">
                Selective analysis completed on {format(new Date(analysisData.timestamp), "PPP 'at' p")}
              </p>

              {/* Compliance Progress Bar */}
              <div className="w-full bg-gray-200 rounded-full h-3 mb-4">
                <div
                  className={`h-3 rounded-full ${
                    analysisData.summary.compliancePercentage < 50 ? "bg-red-500" :
                    analysisData.summary.compliancePercentage < 80 ? "bg-yellow-500" : "bg-green-500"
                  }`}
                  style={{ width: `${analysisData.summary.compliancePercentage}%` }}
                ></div>
              </div>
            </div>

            {/* Analysis Results Component */}
            <AnalysisResults
              results={analysisData.results}
              parameters={analysisData.parameters}
              documents={analysisData.documents}
            />
          </div>
        )
      )}
    </div>
  );
};

export default Analyzer;